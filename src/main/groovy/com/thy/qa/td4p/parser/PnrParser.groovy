package com.thy.qa.td4p.parser

import com.thy.qa.td4p.baseclasses.FlightSegment
import com.thy.qa.td4p.baseclasses.OriginDestinationOption
import com.thy.qa.td4p.enums.AirlineCode
import com.thy.qa.td4p.enums.BrandCode
import com.thy.qa.td4p.enums.Gender
import com.thy.qa.td4p.enums.Nationality
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.pnr.PassengerInfo
import com.thy.qa.td4p.pnr.Pnr
import groovy.xml.XmlSlurper
import groovy.xml.slurpersupport.GPathResult

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class PnrParser {

    private final GPathResult xml

    PnrParser(String response) {
        this.xml = new XmlSlurper(false, true).parseText(response)
    }

    Pnr parse() {
        Pnr pnr = new Pnr()
        pnr.setOriginDestinationOptions(parseOriginDestinationOptions())
        pnr.setPassengers(parsePassengers())
        GPathResult bookingReferenceID = (GPathResult) xml.depthFirst().find { GPathResult it -> it.name() == 'BookingReferenceID' && it.getProperty('@Type') == 'PNR' }
        pnr.setPnrNumber(bookingReferenceID.getProperty('@ID').toString())
        GPathResult grandTotal = (GPathResult) xml.depthFirst().find { GPathResult it -> it.name() == 'GrandTotal' }
        pnr.setCurrencyCode(grandTotal.getProperty('@CurrencyCode').toString())
        pnr.setTotalAmount(((GPathResult) grandTotal.getProperty('@Amount')).toBigDecimal())
        return pnr
    }

    private List<OriginDestinationOption> parseOriginDestinationOptions() {
        List<OriginDestinationOption> originDestinationOptions = xml.depthFirst().findAll { GPathResult it -> it.name() == 'OriginDestinationOption' && ((GPathResult) ((GPathResult) it.getProperty('FlightSegment'))[0]).getProperty('Comment').toString() != 'ARNK'
        }.collect { originDestinationOptionNode -> new OriginDestinationOption(parseFlightSegments((GPathResult) originDestinationOptionNode)) }
        List<GPathResult> fareInfoNodes = (List) xml.depthFirst().findAll { GPathResult it -> it.name() == 'FareInfo' }
        if (fareInfoNodes.size()) {
            List<FlightSegment> flightSegments = (List) originDestinationOptions*.getFlightSegments().flatten()
            fareInfoNodes.each { fareInfoNode ->
                GPathResult fareInfo = (GPathResult) fareInfoNode
                int rph = ((GPathResult) fareInfo.getProperty('@RPH')).toInteger()
                String brandCode = ((GPathResult) fareInfo.getProperty('@RuleNumber')).toString()
                FlightSegment flightSegment = (FlightSegment) flightSegments.find { ((FlightSegment) it).getRph() == rph }
                flightSegment.setBrandCode(BrandCode.valueOf(brandCode))
            }
        }
        return originDestinationOptions
    }

    private List<PassengerInfo> parsePassengers() {
        List<String> ticketNumbers = xml.depthFirst().findAll { GPathResult it -> it.name() == 'Ticketing'
        }*.@TicketDocumentNbr*.text().reverse()

        List<PassengerInfo> passengers = xml.depthFirst().findAll { GPathResult it -> it.name() == 'AirTraveler'
        }.withIndex().collect { node, index ->
            GPathResult passengerNode = (GPathResult) node
            String passengerTypeCode = passengerNode.getProperty('@PassengerTypeCode').toString()
            String birthDateText = passengerNode.getProperty('@BirthDate').toString()
            LocalDate birthDate = LocalDate.parse(birthDateText, DateTimeFormatter.ISO_OFFSET_DATE)
            boolean accompaniedByInfant = ((GPathResult) passengerNode.getProperty('@AccompaniedByInfantInd')).toBoolean()
            String genderText = passengerNode.getProperty('@Gender').toString()
            Gender gender = Gender.from(genderText)
            GPathResult personNameNode = (GPathResult) passengerNode.getProperty('PersonName')
            String namePrefix = ((GPathResult) personNameNode.getProperty('NamePrefix')).toString()
            String givenName = ((GPathResult) personNameNode.getProperty('GivenName')).toString()
            String surname = ((GPathResult) personNameNode.getProperty('Surname')).toString()
            String middleName = ((GPathResult) personNameNode.getProperty('MiddleName')).toString()

            GPathResult documentNode = (GPathResult) passengerNode.getProperty('Document')
            tcNo = documentNode.getProperty('@DocID').toString()

            return new PassengerInfo(PassengerCode.valueOf(passengerTypeCode),
                    givenName,
                    surname,
                    ticketNumbers?[index],
                    namePrefix,
                    middleName,
                    birthDate,
                    gender,
                    Nationality.TR, // Default nationality
                    tcNo,
                    null, // email - not available in PNR XML typically
                    null, // mobilePhoneNumber - not available in PNR XML typically
                    accompaniedByInfant,
                    '' // msNo - not available in PNR XML typically
            )
        }
        return passengers
    }

    private List<FlightSegment> parseFlightSegments(GPathResult originDestinationOptionNode) {
        GPathResult flightSegmentNodes = (GPathResult) originDestinationOptionNode.getProperty('FlightSegment')
        return flightSegmentNodes.iterator().collect { parseFlightSegment((GPathResult) it) }
    }

    private FlightSegment parseFlightSegment(GPathResult flightSegmentNode) {
        String flightNumber = flightSegmentNode.getProperty('@FlightNumber').toString()
        String departureAirportCode = ((GPathResult) flightSegmentNode.getProperty('DepartureAirport')).getProperty('@LocationCode').toString()
        String arrivalAirportCode = ((GPathResult) flightSegmentNode.getProperty('ArrivalAirport')).getProperty('@LocationCode').toString()
        String operatingAirlineCode = ((GPathResult) flightSegmentNode.getProperty('OperatingAirline')).getProperty('@Code').toString()
        String departureDateTimeText = flightSegmentNode.getProperty('@DepartureDateTime').toString()
        LocalDateTime departureDateTime = ZonedDateTime.parse(departureDateTimeText).toLocalDateTime()
        String arrivalDateTimeText = flightSegmentNode.getProperty('@ArrivalDateTime').toString()
        LocalDateTime arrivalDateTime = ZonedDateTime.parse(arrivalDateTimeText).toLocalDateTime()
        String fareBasisCode = flightSegmentNode.getProperty('@FareBasisCode').toString()
        String resBookDesigCode = flightSegmentNode.getProperty('@ResBookDesigCode').toString()
        Boolean validConnectionIndicator = ((GPathResult) flightSegmentNode.getProperty('@ValidConnectionInd')).toBoolean()
        FlightSegment flightSegment = new FlightSegment([departureAirport        : departureAirportCode,
                                                         arrivalAirport          : arrivalAirportCode,
                                                         airlineCode             : AirlineCode.valueOf(flightNumber.take(2)),
                                                         flightNumber            : flightNumber.drop(2),
                                                         operatingAirlineCode    : operatingAirlineCode,
                                                         departureDateTime       : departureDateTime,
                                                         arrivalDateTime         : arrivalDateTime,
                                                         fareBasisCode           : fareBasisCode,
                                                         resBookDesigCode        : resBookDesigCode,
                                                         validConnectionIndicator: validConnectionIndicator,
                                                         brandCode               : null,])
        int rph = ((GPathResult) flightSegmentNode.getProperty('@RPH')).toInteger()
        flightSegment.setRph(rph)
        return flightSegment
    }
}
