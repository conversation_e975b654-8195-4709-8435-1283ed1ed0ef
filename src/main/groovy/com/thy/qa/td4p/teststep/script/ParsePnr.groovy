package com.thy.qa.td4p.teststep.script

import com.eviware.soapui.impl.wsdl.testcase.WsdlTestCaseRunner
import com.eviware.soapui.impl.wsdl.testcase.WsdlTestRunContext
import com.eviware.soapui.impl.wsdl.teststeps.WsdlTestStep
import com.thy.qa.td4p.pnr.Pnr
import com.thy.qa.td4p.baseclasses.TestStepFinder
import com.thy.qa.td4p.parser.PnrParser
import groovy.transform.CompileStatic
import org.apache.logging.log4j.core.Logger

@CompileStatic
class ParsePnr extends ScriptTestStep implements SetPnrAmountCurrencyCode {

    ParsePnr(Logger log, WsdlTestRunContext context, WsdlTestCaseRunner testRunner) {
        super(log, context, testRunner)
    }

    @Override
    void run() {
        TestStepFinder testStepFinder = new TestStepFinder(context)
        WsdlTestStep referredStep = testStepFinder.findSuccessfulStepOfType('retrieveReservationDetail')
        Pnr pnr = new PnrParser(referredStep.getPropertyValue('Response')).parse()
        context.pnr = pnr
    }
}
