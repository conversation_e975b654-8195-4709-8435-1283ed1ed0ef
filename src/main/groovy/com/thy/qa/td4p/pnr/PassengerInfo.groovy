package com.thy.qa.td4p.pnr

import com.thy.qa.td4p.enums.Gender
import com.thy.qa.td4p.enums.Nationality
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.passenger.contact.MobilePhoneNumber

import java.time.LocalDate

/**
<ns2:Document xmlns:ns2="http://www.opentravel.org/OTA/2003/05" DocID="0" Remark="TCKN"/> <ns2:Document xmlns:ns2="http://www.opentravel.org/OTA/2003/05" DocID="" DocHolderNationality="" DocIssueCountry="" Gender="F" DocType="P" BirthDate="1990-06-19+03:00" Remark="DOCS"> <ns2:DocHolderFormattedName> <ns2:GivenName>DENEME</ns2:GivenName> <ns2:Surname>DENEME</ns2:Surname> </ns2:DocHolderFormattedName> </ns2:Document>
<ns2:Document xmlns:ns2="http://www.opentravel.org/OTA/2003/05" DocID="11387724014" Remark="TCKN"/>
<#comment #comment="?xml version="1.0" encoding="UTF-8"?"></#comment>
<ns2:Document xmlns:ns2="http://www.opentravel.org/OTA/2003/05" DocID="" DocHolderNationality="" DocIssueCountry="" Gender="M" DocType="P" BirthDate="1966-03-23+03:00" Remark="DOCS"> <ns2:DocHolderFormattedName> <ns2:GivenName>CIHAD</ns2:GivenName> <ns2:Surname>HAN</ns2:Surname> </ns2:DocHolderFormattedName> </ns2:Document>
*/
record PassengerInfo(
        PassengerCode passengerCode,
        String firstName,
        String lastName,
        String ticketNumber,
        String namePrefix,
        String middleName,
        LocalDate birthDate,
        Gender gender,
        Nationality nationality,
        String tcNo,
        String email,
        MobilePhoneNumber mobilePhoneNumber,
        boolean accompaniedByInfant,
        String msNo
) {
    @Override
    String toString() {
        return "PassengerInfo(passengerCode=${passengerCode}, firstName='${firstName}', lastName='${lastName}', ticketNumber='${ticketNumber}', namePrefix='${namePrefix}', birthDate=${birthDate}, gender=${gender})"
    }

    boolean hasExtraSeat() {
        return true
    }
}