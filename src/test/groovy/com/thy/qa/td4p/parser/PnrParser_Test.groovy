package com.thy.qa.td4p.parser

import com.thy.qa.td4p.enums.Gender
import com.thy.qa.td4p.enums.Nationality
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.pnr.PassengerInfo
import com.thy.qa.td4p.pnr.Pnr
import org.junit.Test

import java.time.LocalDate

class PnrParser_Test {

    @Test
    void parse_shouldReturnCorrectPnrWithPassengerInfo() {
        String responseContent = new File(this.class.getResource('/response/PurchaseBasketResponse.xml').toURI()).text
        PnrParser pnrParser = new PnrParser(responseContent)
        
        Pnr pnr = pnrParser.parse()
        
        assert pnr != null
        assert pnr.pnrNumber == 'VRHD8Q'
        assert pnr.currencyCode == 'TRY'
        assert pnr.totalAmount == 6191.15
        
        // Verify passengers
        assert pnr.passengers.size() == 2
        
        // Verify first passenger (ADULT)
        PassengerInfo firstPassenger = pnr.passengers[0]
        firstPassenger.with {
            assert passengerCode() == PassengerCode.ADULT
            assert firstName() == "BUNYAMIN"
            assert lastName() == "ERTAS"
            assert ticketNumber() == "2352271593159"
            assert namePrefix() == "MS"
            assert birthDate() == LocalDate.parse("1990-10-20")
            assert gender() == Gender.F
            assert nationality() == Nationality.TR
            assert accompaniedByInfant() == true
            assert tcNo() == "39274984174"
        }
        
        // Verify second passenger (INFANT)
        PassengerInfo secondPassenger = pnr.passengers[1]
        secondPassenger.with {
            assert passengerCode() == PassengerCode.INFANT
            assert firstName() == "EMRE"
            assert lastName() == "CEVAHIR"
            assert ticketNumber() == "2352271593160"
            assert namePrefix() == "MSTR"
            assert birthDate() == LocalDate.parse("2024-01-17")
            assert gender() == Gender.M
            assert nationality() == Nationality.TR
            assert accompaniedByInfant() == false
        }
        
        // Verify origin destination options
        assert pnr.originDestinationOptions.size() == 1
        assert pnr.originDestinationOptions[0].flightSegments.size() == 1
        
        def flightSegment = pnr.originDestinationOptions[0].flightSegments[0]
        assert flightSegment.departureAirport == "IST"
        assert flightSegment.arrivalAirport == "AMS"
        assert flightSegment.flightNumber == "1951"
        assert flightSegment.fareBasisCode == "V"
        assert flightSegment.resBookDesigCode == "Y"
    }

    @Test
    void parsePassengers_shouldHandlePassengersWithoutTicketNumbers() {
        String xmlContent = '''<?xml version="1.0" encoding="UTF-8"?>
        <ns2:OTA_AirBookRS xmlns:ns2="http://www.opentravel.org/OTA/2003/05">
            <ns2:AirReservation>
                <ns2:TravelerInfo>
                    <ns2:AirTraveler BirthDate="1990-10-20+03:00" PassengerTypeCode="ADULT"
                      AccompaniedByInfantInd="false" Gender="FEMALE">
                      <ns2:PersonName>
                        <ns2:NamePrefix>MR</ns2:NamePrefix>
                        <ns2:GivenName>JOHN</ns2:GivenName>
                        <ns2:Surname>DOE</ns2:Surname>
                      </ns2:PersonName>
                    </ns2:AirTraveler>
                </ns2:TravelerInfo>
                <ns2:BookingReferenceID Type="PNR" ID="TEST123"/>
                <ns2:PriceInfo>
                    <ns2:PTC_FareBreakdowns>
                        <ns2:GrandTotal CurrencyCode="USD" Amount="500.00"/>
                    </ns2:PTC_FareBreakdowns>
                </ns2:PriceInfo>
            </ns2:AirReservation>
        </ns2:OTA_AirBookRS>'''
        
        PnrParser pnrParser = new PnrParser(xmlContent)
        Pnr pnr = pnrParser.parse()
        
        assert pnr.passengers.size() == 1
        PassengerInfo passenger = pnr.passengers[0]
        passenger.with {
            assert passengerCode() == PassengerCode.ADULT
            assert firstName() == "JOHN"
            assert lastName() == "DOE"
            assert ticketNumber() == ""
            assert namePrefix() == "MR"
            assert gender() == Gender.F
        }
    }

    @Test
    void parsePassengers_shouldParseMultiplePassengersCorrectly() {
        String xmlContent = '''<?xml version="1.0" encoding="UTF-8"?>
        <ns2:OTA_AirBookRS xmlns:ns2="http://www.opentravel.org/OTA/2003/05">
            <ns2:AirReservation>
                <ns2:TravelerInfo>
                    <ns2:AirTraveler BirthDate="1985-05-15+03:00" PassengerTypeCode="ADULT"
                      AccompaniedByInfantInd="false" Gender="MALE">
                      <ns2:PersonName>
                        <ns2:NamePrefix>MR</ns2:NamePrefix>
                        <ns2:GivenName>ALEX</ns2:GivenName>
                        <ns2:Surname>SMITH</ns2:Surname>
                      </ns2:PersonName>
                    </ns2:AirTraveler>
                    <ns2:AirTraveler BirthDate="2015-03-10+03:00" PassengerTypeCode="CHILD"
                      AccompaniedByInfantInd="false" Gender="FEMALE">
                      <ns2:PersonName>
                        <ns2:NamePrefix>MISS</ns2:NamePrefix>
                        <ns2:GivenName>EMMA</ns2:GivenName>
                        <ns2:Surname>SMITH</ns2:Surname>
                      </ns2:PersonName>
                    </ns2:AirTraveler>
                </ns2:TravelerInfo>
                <ns2:Ticketing TicketDocumentNbr="1234567890"/>
                <ns2:Ticketing TicketDocumentNbr="0987654321"/>
                <ns2:BookingReferenceID Type="PNR" ID="ABC123"/>
                <ns2:PriceInfo>
                    <ns2:PTC_FareBreakdowns>
                        <ns2:GrandTotal CurrencyCode="EUR" Amount="750.50"/>
                    </ns2:PTC_FareBreakdowns>
                </ns2:PriceInfo>
            </ns2:AirReservation>
        </ns2:OTA_AirBookRS>'''
        
        PnrParser pnrParser = new PnrParser(xmlContent)
        Pnr pnr = pnrParser.parse()
        
        assert pnr.passengers.size() == 2
        
        // First passenger (ADULT)
        PassengerInfo adult = pnr.passengers[0]
        adult.with {
            assert passengerCode() == PassengerCode.ADULT
            assert firstName() == "ALEX"
            assert lastName() == "SMITH"
            assert ticketNumber() == "0987654321" // reversed order
            assert namePrefix() == "MR"
            assert gender() == Gender.M
            assert birthDate() == LocalDate.parse("1985-05-15")
        }
        
        // Second passenger (CHILD)
        PassengerInfo child = pnr.passengers[1]
        child.with {
            assert passengerCode() == PassengerCode.CHILD
            assert firstName() == "EMMA"
            assert lastName() == "SMITH"
            assert ticketNumber() == "1234567890" // reversed order
            assert namePrefix() == "MISS"
            assert gender() == Gender.F
            assert birthDate() == LocalDate.parse("2015-03-10")
        }
    }
}
