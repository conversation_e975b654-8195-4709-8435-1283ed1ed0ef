package com.thy.qa.td4p

import com.thy.qa.td4p.enums.Gender
import com.thy.qa.td4p.enums.Nationality
import com.thy.qa.td4p.passenger.PassengerCode
import com.thy.qa.td4p.pnr.PassengerInfo
import groovy.json.JsonSlurper
import groovy.xml.XmlSlurper
import groovy.xml.XmlUtil
import org.junit.Test

import java.time.LocalDate

class GetPnr_Test {

    @Test
    void test() {
        List a = new JsonSlurper().parse(new File('purchaseBasketResponses.json')).results.
                'fedf7161-6b29-4265-b5b6-d2deb1c06dec'.search_types.'12ba7548-2042-4ad7-bab7-e4f9d94a23af'
                .messages.findAll { it ->
            it.message.responseStatus == 'SUCCESS'
        }.collect {
            it.message.message
        }
        def b = a.collect {
            new XmlSlurper().parseText(it).'**'.findAll {
                it.name() == 'Document'
            }
        }
        b.flatten().each {println(XmlUtil.serialize(it))}
    }

    @Test
    void getPassengerInfo_shouldReturnCorrectPassengerInfo() {

        String responseContent = new File(this.class.getResource('/response/PurchaseBasketResponse.xml').toURI()).text

        GetPnr getPnr = [getTestStepResponseAt: { int index -> return responseContent
        }] as GetPnr

        List<PassengerInfo> passengerInfoList = getPnr.getPassengerInfo()

        assert passengerInfoList.size() == 2

        // Verify first passenger (ADULT)
        PassengerInfo firstPassenger = passengerInfoList[0]
        firstPassenger.with {
            assert passengerCode() == PassengerCode.ADULT
            assert firstName() == "BUNYAMIN"
            assert lastName() == "ERTAS"
            assert ticketNumber() == "2352271593159"
            assert namePrefix() == "MS"
            assert birthDate() == LocalDate.parse("1990-10-20")
            assert gender() == Gender.F
            assert nationality() == Nationality.TR
            assert accompaniedByInfant() == true
            assert tcNo() == "39274984174"
        }

        // Verify second passenger (INFANT)
        PassengerInfo secondPassenger = passengerInfoList[1]
        secondPassenger.with {
            assert passengerCode() == PassengerCode.INFANT
            assert firstName() == "EMRE"
            assert lastName() == "CEVAHIR"
            assert ticketNumber() == "2352271593160"
            assert namePrefix() == "MSTR"
            assert birthDate() == LocalDate.parse("2024-01-17")
            assert gender() == Gender.M
            assert nationality() == Nationality.TR
            assert accompaniedByInfant() == false
        }
    }
}